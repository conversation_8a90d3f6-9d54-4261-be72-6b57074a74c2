"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { type FeatureShowcaseBannerProps } from "./types";

export const FeatureShowcaseBanner: React.FC<FeatureShowcaseBannerProps> = ({
  title,
  subtitle,
  description,
  imageUrl,
  imageAlt,
  layout,
  ctaButton,
  backgroundColor = "bg-gradient-to-r from-primary-50 to-secondary-50",
  isActive,
}) => {
  const isTextLeft = layout === "text-left";

  return (
    <div 
      className={cn(
        "w-full h-full min-h-[400px] lg:min-h-[500px] flex items-center",
        backgroundColor
      )}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className={cn(
          "grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center",
          isTextLeft ? "lg:grid-cols-[1fr_0.8fr]" : "lg:grid-cols-[0.8fr_1fr]"
        )}>
          {/* Text Content */}
          <div className={cn(
            "space-y-6",
            isTextLeft ? "lg:order-1" : "lg:order-2",
            "order-2"
          )}>
            {subtitle && (
              <div className="inline-block">
                <span className="text-sm font-medium text-primary-600 bg-primary-100 px-3 py-1 rounded-full">
                  {subtitle}
                </span>
              </div>
            )}
            
            <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-primary-900 leading-tight">
              {title}
            </h1>
            
            <p className="text-lg text-primary-700 leading-relaxed max-w-2xl">
              {description}
            </p>
            
            {ctaButton && (
              <div className="pt-4">
                <Button
                  asChild
                  variant={ctaButton.variant || "default"}
                  size="lg"
                  className="bg-primary-700 hover:bg-primary-800 text-white px-8 py-3 text-base font-medium"
                >
                  <Link href={ctaButton.href}>
                    {ctaButton.text}
                  </Link>
                </Button>
              </div>
            )}
          </div>

          {/* Image Content */}
          <div className={cn(
            "relative",
            isTextLeft ? "lg:order-2" : "lg:order-1",
            "order-1"
          )}>
            <div className="relative aspect-[4/3] lg:aspect-[3/4] rounded-2xl overflow-hidden shadow-2xl">
              <Image
                src={imageUrl}
                alt={imageAlt}
                fill
                className="object-cover transition-transform duration-700 hover:scale-105"
                priority={isActive}
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 40vw"
              />
              
              {/* Decorative overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent" />
            </div>
            
            {/* Decorative elements */}
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-secondary-200 rounded-full opacity-20 blur-xl" />
            <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-primary-200 rounded-full opacity-20 blur-xl" />
          </div>
        </div>
      </div>
    </div>
  );
};
