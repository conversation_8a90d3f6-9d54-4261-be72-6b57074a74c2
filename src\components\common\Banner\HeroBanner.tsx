"use client";

import React, { useState, useEffect, useRef } from "react";
import { NAVBAR_HEIGHT } from "@/lib/constants";
import { cn } from "@/lib/utils";
import { type HeroBannerProps, type BannerProps, BannerType } from "./types";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselPrevious,
  CarouselNext,
  type CarouselApi,
} from "@/components/ui/carousel";
import { FeatureShowcaseBanner } from "./FeatureShowcaseBanner";
import { PromotionalBanner } from "./PromotionalBanner";
import { ImageOverlayBanner } from "./ImageOverlayBanner";
import { PropertyShowcaseBanner } from "./PropertyShowcaseBanner";

export const HeroBanner: React.FC<HeroBannerProps> = ({
  banners,
  autoPlay = true,
  autoPlayInterval = 5000,
  showNavigation = true,
  showDots = true,
  className,
  children,
}) => {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);

  // Auto-play setup
  const autoplayPlugin = useRef(
    autoPlay && banners.length > 1
      ? require("embla-carousel-autoplay").default({ delay: autoPlayInterval })
      : null,
  );

  useEffect(() => {
    if (!api) return;

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  // Render individual banner based on type
  const renderBanner = (banner: BannerProps, isActive: boolean) => {
    switch (banner.type) {
      case BannerType.FEATURE_SHOWCASE:
        return <FeatureShowcaseBanner {...banner} isActive={isActive} />;
      case BannerType.PROMOTIONAL:
        return <PromotionalBanner {...banner} isActive={isActive} />;
      case BannerType.IMAGE_OVERLAY:
        return <ImageOverlayBanner {...banner} isActive={isActive} />;
      case BannerType.PROPERTY_SHOWCASE:
        return <PropertyShowcaseBanner {...banner} isActive={isActive} />;
      default:
        return null;
    }
  };

  if (!banners || banners.length === 0) {
    return null;
  }

  // If only one banner, render without carousel
  if (banners.length === 1) {
    return (
      <section
        className={cn("relative w-full", className)}
        style={{
          marginTop: `${NAVBAR_HEIGHT}px`,
          minHeight: "400px",
        }}
      >
        {renderBanner(banners[0]!, true)}
        {children}
      </section>
    );
  }

  // Multiple banners - use carousel
  return (
    <section
      className={cn("relative w-full", className)}
      style={{
        marginTop: `${NAVBAR_HEIGHT}px`,
        minHeight: "400px",
      }}
    >
      <Carousel
        setApi={setApi}
        className="h-full w-full"
        opts={{
          align: "start",
          loop: true,
        }}
        plugins={autoplayPlugin.current ? [autoplayPlugin.current] : []}
      >
        <CarouselContent className="h-full">
          {banners.map((banner, index) => (
            <CarouselItem key={banner.id} className="h-full">
              {renderBanner(banner, index === current - 1)}
            </CarouselItem>
          ))}
        </CarouselContent>

        {/* Navigation arrows */}
        {showNavigation && (
          <>
            <CarouselPrevious className="absolute top-1/2 left-4 z-10 -translate-y-1/2 border-white/20 bg-white/90 shadow-lg backdrop-blur-sm hover:bg-white" />
            <CarouselNext className="absolute top-1/2 right-4 z-10 -translate-y-1/2 border-white/20 bg-white/90 shadow-lg backdrop-blur-sm hover:bg-white" />
          </>
        )}

        {/* Dot indicators */}
        {showDots && (
          <div className="absolute bottom-6 left-1/2 z-10 -translate-x-1/2">
            <div className="flex items-center gap-2 rounded-full bg-black/20 px-4 py-2 backdrop-blur-sm">
              {banners.map((_, index) => (
                <button
                  key={index}
                  className={cn(
                    "h-2 w-2 rounded-full transition-all duration-300 hover:scale-125",
                    index === current - 1
                      ? "scale-125 bg-white"
                      : "bg-white/50 hover:bg-white/75",
                  )}
                  onClick={() => api?.scrollTo(index)}
                  aria-label={`Go to banner ${index + 1}`}
                />
              ))}
            </div>
          </div>
        )}
      </Carousel>

      {/* Custom children content */}
      {children}
    </section>
  );
};
