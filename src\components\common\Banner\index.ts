// Main Hero Banner Component
export { HeroBanner } from "./HeroBanner";

// Individual Banner Components
export { FeatureShowcaseBanner } from "./FeatureShowcaseBanner";
export { PromotionalBanner } from "./PromotionalBanner";
export { ImageOverlayBanner } from "./ImageOverlayBanner";
export { PropertyShowcaseBanner } from "./PropertyShowcaseBanner";

// Navigation Component (deprecated - now using shadcn carousel)
export { BannerNavigation } from "./BannerNavigation";

// Types and Interfaces
export * from "./types";

// Re-export for convenience
export type {
  HeroBannerProps,
  BannerProps,
  FeatureShowcaseBannerProps,
  PromotionalBannerProps,
  ImageOverlayBannerProps,
  PropertyShowcaseBannerProps,
  BannerNavigationProps,
} from "./types";

export { BannerType } from "./types";
