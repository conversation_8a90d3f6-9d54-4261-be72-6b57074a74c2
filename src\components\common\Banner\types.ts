import { type ReactNode } from "react";

// Base banner interface
export interface BaseBannerProps {
  id: string;
  type: BannerType;
  priority?: number;
  isActive?: boolean;
}

// Banner types enum
export enum BannerType {
  FEATURE_SHOWCASE = "feature-showcase",
  PROMOTIONAL = "promotional",
  IMAGE_OVERLAY = "image-overlay",
  PROPERTY_SHOWCASE = "property-showcase",
}

// Feature Showcase Banner Props (formerly Left-Right Text)
export interface FeatureShowcaseBannerProps extends BaseBannerProps {
  type: BannerType.FEATURE_SHOWCASE;
  title: string;
  subtitle?: string;
  description: string;
  imageUrl: string;
  imageAlt: string;
  layout: "text-left" | "text-right";
  ctaButton?: {
    text: string;
    href: string;
    variant?: "default" | "outline" | "secondary";
  };
  backgroundColor?: string;
}

// Promotional Banner Props (formerly Text-CTA)
export interface PromotionalBannerProps extends BaseBannerProps {
  type: BannerType.PROMOTIONAL;
  title: string;
  subtitle?: string;
  description?: string;
  primaryCTA: {
    text: string;
    href: string;
    variant?: "default" | "outline" | "secondary";
  };
  secondaryCTA?: {
    text: string;
    href: string;
    variant?: "default" | "outline" | "secondary";
  };
  backgroundColor?: string;
  backgroundImage?: string;
}

// Image Overlay Banner Props
export interface ImageOverlayBannerProps extends BaseBannerProps {
  type: BannerType.IMAGE_OVERLAY;
  title: string;
  subtitle?: string;
  description?: string;
  backgroundImage: string;
  overlayOpacity?: number;
  textPosition?: "center" | "left" | "right";
  ctaButton?: {
    text: string;
    href: string;
    variant?: "default" | "outline" | "secondary";
  };
}

// Property Showcase Banner Props
export interface PropertyShowcaseBannerProps extends BaseBannerProps {
  type: BannerType.PROPERTY_SHOWCASE;
  property: {
    id: string;
    title: string;
    address: string;
    price: number;
    priceUnit: "month" | "week" | "day";
    bedrooms?: number;
    bathrooms?: number;
    area?: number;
    areaUnit?: "sqft" | "sqm";
    images: string[];
    features?: string[];
  };
  ctaButton: {
    text: string;
    href: string;
    variant?: "default" | "outline" | "secondary";
  };
  showFeatures?: boolean;
}

// Union type for all banner props
export type BannerProps =
  | FeatureShowcaseBannerProps
  | PromotionalBannerProps
  | ImageOverlayBannerProps
  | PropertyShowcaseBannerProps;

// Hero Banner Wrapper Props
export interface HeroBannerProps {
  banners: BannerProps[];
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showNavigation?: boolean;
  showDots?: boolean;
  className?: string;
  children?: ReactNode;
}

// Navigation Props
export interface BannerNavigationProps {
  currentIndex: number;
  totalBanners: number;
  onPrevious: () => void;
  onNext: () => void;
  onDotClick: (index: number) => void;
  showDots?: boolean;
  showArrows?: boolean;
}
